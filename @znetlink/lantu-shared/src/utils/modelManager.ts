import { AxiosInstance } from 'axios';
import { ModelType, ModelManagerConfig } from '../types';

export const SCENARIO = "SCENARIO";
export const PLATFORM = "PLATFORM";
export const DEVICE = "DEVICE";
export const LINK = "LINK";
export const MODULATION = "MODULATION";
export const TRAFFIC = "TRAFFIC";
export const ITMPARAM = "ITMPARAM";

export class ModelManager {
  private static instance: ModelManager;
  private api: AxiosInstance;
  private downloadUrl: string;
  private listUrl: string;
  private uploadUrl: string;
  private deleteUrl: string;
  
  private modelSuffixMap = {
    SCENARIO: "scen.m",
    PLATFORM: "plt.m",
    DEVICE: "dev.m",
    LINK: "link.m",
    MODULATION: "md.m",
    TRAFFIC: "traf.m",
    ITMPARAM: "itmp.m",
  };

  private modelCache = {
    SCENARIO: new Map(),
    PLATFORM: new Map(),
    DEVICE: new Map(),
    LINK: new Map(),
    MODULATION: new Map(),
    TRAFFIC: new Map(),
    ITMPARAM: new Map(),
  };

  constructor(config: ModelManagerConfig) {
    this.api = config.api;
    this.downloadUrl = config.downloadUrl || "/storage/download";
    this.listUrl = config.listUrl || "/storage/list";
    this.uploadUrl = config.uploadUrl || "/storage/upload";
    this.deleteUrl = config.deleteUrl || "/storage/delete";
  }

  static getInstance(config?: ModelManagerConfig): ModelManager {
    if (!ModelManager.instance) {
      if (!config) {
        throw new Error('ModelManager config is required for first initialization');
      }
      ModelManager.instance = new ModelManager(config);
    }
    return ModelManager.instance;
  }

  async getModel(modelType: ModelType, modelName: string): Promise<any> {
    if (this.modelCache[modelType].has(modelName)) {
      return JSON.parse(JSON.stringify(this.modelCache[modelType].get(modelName)));
    }

    return await this.loadModel(modelType, modelName);
  }

  async loadModel(modelType: ModelType, modelName: string): Promise<any> {
    const modelUrl = `${this.downloadUrl}?filename=/models/${modelType.toLowerCase()}s/${modelName}.${
      this.modelSuffixMap[modelType]
    }`;
    const response = await this.api.get(modelUrl);
    if (response.status === 200) {
      // 判断如果是字符串，转换为json对象
      if (typeof response.data === "string") {
        console.log("response is string, convert to json", response.data);
        response.data = JSON.parse(response.data);
      }
      this.modelCache[modelType].set(modelName, response.data);
      return JSON.parse(JSON.stringify(response.data));
    } else {
      console.error(`Failed to load model: ${modelName}`, response);
      throw new Error(`Failed to load model: ${modelName}`);
    }
  }

  async saveModel(modelType: ModelType, modelName: string, modelData: string): Promise<any> {
    if (modelName.endsWith(this.modelSuffixMap[modelType])) {
      modelName = modelName.replaceAll("." + this.modelSuffixMap[modelType], "");
    }
    const filename = `/models/${modelType.toLowerCase()}s/${modelName}.${this.modelSuffixMap[modelType]}`;

    // 从缓存中删除
    if (this.modelCache[modelType].has(modelName)) {
      this.modelCache[modelType].delete(modelName);
    }

    // 以formData形式上传数据，后端有一个名称为file的文件接收参数
    const formData = new FormData();
    formData.append("file", new Blob([modelData]), filename);
    return await this.api.post(this.uploadUrl, formData);
  }

  async deleteModel(modelType: ModelType, modelName: string): Promise<any> {
    if (modelName.endsWith(this.modelSuffixMap[modelType])) {
      modelName = modelName.replaceAll("." + this.modelSuffixMap[modelType], "");
    }

    if (this.modelCache[modelType].has(modelName)) {
      this.modelCache[modelType].delete(modelName);
    }

    const filename = `/models/${modelType.toLowerCase()}s/${modelName}.${this.modelSuffixMap[modelType]}`;
    return this.api.post(`${this.deleteUrl}?filename=${filename}`);
  }

  async listModel(modelType: ModelType): Promise<any[]> {
    const res = await this.api.get("/storage/list?prefix=/models/" + modelType.toLowerCase());

    if (res.status !== 200) {
      console.error("Failed to list models", res);
      return [];
    }
    // res.data 有 private和public两个数组，将它们合并并在每个对象中添加一个visibility字段
    const data = res.data.private.concat(res.data.public).map((item: any) => {
      item.visibility = item.public ? "公有模型" : "个人模型";
      // 从带path的fileName中提取的文件名
      item.name = item.fileName.split("/").pop();
      return item;
    });

    return data;
  }

  removeModelSuffix(modelName: string): string {
    for (const modelType in this.modelSuffixMap) {
      if (modelName.endsWith(this.modelSuffixMap[modelType as ModelType])) {
        return modelName.replaceAll("." + this.modelSuffixMap[modelType as ModelType], "");
      }
    }
    return modelName;
  }
}

// 创建一个工厂函数来创建 ModelManager 实例
export function createModelManager(config: ModelManagerConfig): ModelManager {
  return ModelManager.getInstance(config);
}
