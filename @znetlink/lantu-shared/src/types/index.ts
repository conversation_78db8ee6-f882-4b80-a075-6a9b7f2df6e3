import { AxiosInstance } from 'axios';

// 模型类型常量
export const SCENARIO = "SCENARIO";
export const PLATFORM = "PLATFORM";
export const DEVICE = "DEVICE";
export const LINK = "LINK";
export const MODULATION = "MODULATION";
export const TRAFFIC = "TRAFFIC";
export const ITMPARAM = "ITMPARAM";

export type ModelType = typeof SCENARIO | typeof PLATFORM | typeof DEVICE | typeof LINK | typeof MODULATION | typeof TRAFFIC | typeof ITMPARAM;

// 模型管理器配置接口
export interface ModelManagerConfig {
  api: AxiosInstance;
  downloadUrl?: string;
  listUrl?: string;
  uploadUrl?: string;
  deleteUrl?: string;
}

// 设备属性接口
export interface DeviceAttribute {
  name: string;
  label?: string;
  type: 'string' | 'int' | 'float' | 'bool' | 'option' | 'object' | 'array';
  defaultValue?: any;
  unit?: string;
  hidden?: boolean;
  options?: Array<{ key: string; value: any }>;
  children?: DeviceAttribute[];
  elementType?: DeviceAttribute;
  symbolMap?: Array<{ key: string; value: any }>;
}

// 设备模型接口
export interface DeviceModel {
  attributes?: DeviceAttribute[];
  [key: string]: any;
}

// 属性树节点接口
export interface AttributeTreeNode {
  label: string;
  icon: string;
  attr?: DeviceAttribute & {
    deviceId: string;
    path: string;
    isArrayItem?: boolean;
    arrayIndex?: number;
  };
  value?: any;
  children?: AttributeTreeNode[];
}

// DeviceAttributeTree 组件的 Props 接口
export interface DeviceAttributeTreeProps {
  deviceId: string;
  deviceModel: DeviceModel;
  platformAttributes?: Record<string, any>;
  deviceAttributes?: Record<string, any>;
}

// DeviceAttributeTree 组件的 Emits 接口
export interface DeviceAttributeTreeEmits {
  'update:attributes': (attributes: Record<string, any>) => void;
}
