<!-- DeviceAttributeTree.vue -->
<template>
  <q-tree
    :nodes="treeData"
    node-key="label"
    label-key="label"
    dense
    :no-nodes-label="t('dialog.attribute_editor.no_attr_tips')"
  >
    <template v-slot:default-header="prop">
      <div class="row full-width items-center q-gutter-x-md">
        <div class="col-4 q-ma-none">
          <q-icon :name="prop.node.icon" size="sm" class="q-mr-sm" />
          {{ prop.node.label }}
        </div>

        <template v-if="prop.node.attr">
          <div class="col-1">{{ prop.node.attr.type }}</div>
          <div class="col">
            <q-input
              outlined
              v-if="['string', 'int', 'float'].includes(prop.node.attr.type)"
              v-model="prop.node.value"
              :type="prop.node.attr.type === 'string' ? 'text' : 'number'"
              dense
              @update:model-value="(val) => updateAttributeValue(prop.node.attr.deviceId, prop.node.attr.path, val)"
              :suffix="prop.node.attr.unit"
            />
            <q-select
              v-else-if="prop.node.attr.type === 'option'"
              v-model="prop.node.value"
              :options="prop.node.attr.options"
              option-label="key"
              option-value="value"
              emit-value
              map-options
              outlined
              options-dense
              dense
            >
            </q-select>
            <q-toggle
              v-else-if="prop.node.attr.type === 'bool'"
              v-model="prop.node.value"
              dense
              @update:model-value="(val) => updateAttributeValue(prop.node.attr.deviceId, prop.node.attr.path, val)"
            />
          </div>
          <div class="col-auto">
            <q-btn
              v-if="prop.node.attr.symbolMap"
              flat
              round
              dense
              icon="list"
              size="sm"
              @click="showSymbolMapDialog(prop.node.attr)"
            >
              <q-tooltip>{{ t("dialog.attribute_editor.select_common_value") }}</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              dense
              icon="refresh"
              size="sm"
              @click="resetToDefault(prop.node)"
            >
              <q-tooltip>{{ t("dialog.attribute_editor.reset_to_default") }}</q-tooltip>
            </q-btn>
            <q-btn
              v-if="prop.node.attr.type === 'array'"
              flat
              round
              dense
              icon="add"
              size="sm"
              @click="addArrayItem(prop.node)"
            >
              <q-tooltip>{{ t("dialog.attribute_editor.add_array_item") }}</q-tooltip>
            </q-btn>
            <q-btn
              v-if="prop.node.attr.isArrayItem"
              flat
              round
              dense
              icon="remove"
              size="sm"
              @click="removeArrayItem(prop.node)"
            >
              <q-tooltip>{{ t("dialog.attribute_editor.remove_array_item") }}</q-tooltip>
            </q-btn>
          </div>
        </template>
      </div>
    </template>
  </q-tree>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";
import type { 
  DeviceAttributeTreeProps, 
  DeviceAttributeTreeEmits, 
  DeviceAttribute, 
  AttributeTreeNode 
} from '../types';

const { t } = useI18n();

const props = defineProps<DeviceAttributeTreeProps>();

const emit = defineEmits<DeviceAttributeTreeEmits>();

const $q = useQuasar();

// Computed tree data based on device model and attributes
const treeData = computed(() => {
  if (!props.deviceModel) return [];
  return props.deviceModel?.attributes?.map((attr) => buildAttributeNode(attr)).filter((node) => node !== null) || [];
});

// Build attribute node (recursive function)
function buildAttributeNode(attr: DeviceAttribute, parentPath = ""): AttributeTreeNode | null {
  // 检查属性是否设置为隐藏
  if (attr.hidden && attr.hidden === true) {
    return null;
  }

  const currentPath = parentPath ? `${parentPath}.${attr.name}` : attr.name;
  const value = getAttributeValue(currentPath);

  const node: AttributeTreeNode = {
    label: attr.label || attr.name,
    icon: getAttributeIcon(attr.type),
    attr: { ...attr, deviceId: props.deviceId, path: currentPath },
    value,
    children: [],
  };

  if (attr.type === "object" && attr.children) {
    // 过滤掉hidden为true的子属性
    node.children = attr.children
      .map((child) => buildAttributeNode(child, currentPath))
      .filter((childNode) => childNode !== null) as AttributeTreeNode[];
  } else if (attr.type === "array" && attr.elementType) {
    const arrayValue = value || [];
    node.children = arrayValue.map((_: any, index: number) => ({
      label: `[${index}]`,
      icon: getAttributeIcon(attr.elementType!.type),
      attr: {
        ...attr.elementType!,
        deviceId: props.deviceId,
        path: `${currentPath}[${index}]`,
        isArrayItem: true,
        arrayIndex: index,
      },
      value: arrayValue[index],
    }));
  }

  return node;
}

// Get attribute value
function getAttributeValue(path: string): any {
  const pathParts = path.split(".");

  // 1. Check platform attributes
  const scenarioValue = getNestedValue(props.platformAttributes || {}, pathParts);
  if (scenarioValue !== undefined) return scenarioValue;

  // 2. Check device attributes
  const platformValue = getNestedValue(props.deviceAttributes || {}, pathParts);
  if (platformValue !== undefined) return platformValue;

  // 3. Return default value from device model
  const attribute = findAttributeByPath(props.deviceModel.attributes || [], pathParts);
  return attribute?.defaultValue;
}

// Update attribute value
function updateAttributeValue(deviceId: string, path: string, value: any): void {
  const newAttributes = { ...props.platformAttributes };
  setNestedValue(newAttributes, path.split("."), value);
  emit("update:attributes", newAttributes);
}

// Helper functions remain the same...
function getAttributeIcon(type: string): string {
  const iconMap: Record<string, string> = {
    string: "text_fields",
    int: "numbers",
    float: "numbers",
    bool: "toggle_on",
    option: "list",
    object: "folder",
    array: "view_list",
  };
  return iconMap[type] || "help_outline";
}

// Reset to default value
function resetToDefault(node: AttributeTreeNode): void {
  if (!node.attr) return;
  const { path } = node.attr;
  const attribute = findAttributeByPath(props.deviceModel.attributes || [], path.split("."));

  const newAttributes = { ...props.platformAttributes };
  deleteNestedValue(newAttributes, path.split("."));
  emit("update:attributes", newAttributes);
}

// Add array item
function addArrayItem(node: AttributeTreeNode): void {
  if (!node.attr) return;
  const { path } = node.attr;
  const currentValue = getAttributeValue(path) || [];
  const newAttributes = { ...props.platformAttributes };
  setNestedValue(newAttributes, path.split("."), [...currentValue, null]);
  emit("update:attributes", newAttributes);
}

// Remove array item
function removeArrayItem(node: AttributeTreeNode): void {
  if (!node.attr) return;
  const { path, arrayIndex } = node.attr;
  if (arrayIndex === undefined) return;
  
  const parentPath = path.split("[")[0];
  const currentValue = getAttributeValue(parentPath) || [];
  currentValue.splice(arrayIndex, 1);

  const newAttributes = { ...props.platformAttributes };
  setNestedValue(newAttributes, parentPath.split("."), currentValue);
  emit("update:attributes", newAttributes);
}

// Helper functions (getNestedValue, setNestedValue, deleteNestedValue, findAttributeByPath)
function getNestedValue(obj: Record<string, any>, pathParts: string[]): any {
  return pathParts.reduce((acc, part) => {
    if (acc === undefined) return undefined;
    const arrayMatch = part.match(/(\w+)\[(\d+)\]/);
    if (arrayMatch) {
      const [_, arrayName, index] = arrayMatch;
      return acc[arrayName]?.[parseInt(index)];
    }
    return acc[part];
  }, obj);
}

function setNestedValue(obj: Record<string, any>, pathParts: string[], value: any): void {
  const lastPart = pathParts.pop()!;
  let current = obj;

  for (const part of pathParts) {
    const arrayMatch = part.match(/(\w+)\[(\d+)\]/);
    if (arrayMatch) {
      const [_, arrayName, index] = arrayMatch;
      current[arrayName] = current[arrayName] || [];
      current = current[arrayName][parseInt(index)] = current[arrayName][parseInt(index)] || {};
    } else {
      current = current[part] = current[part] || {};
    }
  }

  current[lastPart] = value;
}

function deleteNestedValue(obj: Record<string, any>, pathParts: string[]): void {
  const lastPart = pathParts.pop()!;
  let current = obj;

  for (const part of pathParts) {
    const arrayMatch = part.match(/(\w+)\[(\d+)\]/);
    if (arrayMatch) {
      const [_, arrayName, index] = arrayMatch;
      if (!current[arrayName]?.[parseInt(index)]) return;
      current = current[arrayName][parseInt(index)];
    } else {
      if (!current[part]) return;
      current = current[part];
    }
  }

  delete current[lastPart];
}

function findAttributeByPath(attributes: DeviceAttribute[], pathParts: string[]): DeviceAttribute | undefined {
  const [current, ...rest] = pathParts;
  const arrayMatch = current.match(/(\w+)\[(\d+)\]/);
  const attrName = arrayMatch ? arrayMatch[1] : current;

  const attribute = attributes.find((attr) => attr.name === attrName);
  if (!attribute) return undefined;

  if (rest.length === 0) return attribute;

  if (attribute.type === "object" && attribute.children) {
    return findAttributeByPath(attribute.children, rest);
  } else if (attribute.type === "array" && attribute.elementType) {
    return attribute.elementType;
  }

  return undefined;
}

// Symbol map dialog
function showSymbolMapDialog(attr: any): void {
  $q.dialog({
    title: t("dialog.attribute_editor.select_common_value_1"),
    message: t("dialog.attribute_editor.to_attr") + attr.label + t("dialog.attribute_editor.select_common_value_2"),
    options: {
      type: "radio",
      items: attr.symbolMap.map((item: any) => ({
        label: item.key + "  " + `【${item.value}】`,
        value: item.value,
      })),
    },
    cancel: t("common.close"),
  }).onOk((val: any) => {
    updateAttributeValue(attr.deviceId, attr.path, val);
  });
}
</script>

<style scoped>
.q-tree__node-header {
  min-height: 48px;
}

.q-tree__node-header-content {
  width: 100%;
}
</style>
