// 导出类型定义
export * from './types';

// 导出工具类
export { ModelManager, createModelManager } from './utils/modelManager';
export { 
  SCENARIO, 
  PLATFORM, 
  DEVICE, 
  LINK, 
  MODULATION, 
  TRAFFIC, 
  ITMPARAM 
} from './utils/modelManager';

// 导出组件
export { default as DeviceAttributeTree } from './components/DeviceAttributeTree.vue';

// Vue 插件安装函数
import type { App } from 'vue';
import DeviceAttributeTree from './components/DeviceAttributeTree.vue';

export interface LantuSharedOptions {
  // 可以在这里添加全局配置选项
}

export function install(app: App, options?: LantuSharedOptions) {
  // 注册全局组件
  app.component('DeviceAttributeTree', DeviceAttributeTree);
  
  // 可以在这里添加全局配置
  if (options) {
    app.config.globalProperties.$lantuShared = options;
  }
}

// 默认导出插件对象
export default {
  install
};
