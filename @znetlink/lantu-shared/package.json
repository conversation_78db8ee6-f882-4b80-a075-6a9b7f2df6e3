{"name": "@znetlink/lantu-shared", "version": "1.0.0", "description": "蓝图共享组件库 - 用于蓝图主应用和插件之间共享的前端功能", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "prepare": "npm run build"}, "keywords": ["lantu", "blueprint", "vue", "quasar", "shared", "components"], "author": "ZNetLink", "license": "MIT", "peerDependencies": {"vue": "^3.4.0", "quasar": "^2.16.0", "vue-i18n": "^10.0.0", "axios": "^1.2.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@vue/compiler-sfc": "^3.4.0", "rollup": "^4.0.0", "rollup-plugin-vue": "^6.0.0", "typescript": "^5.0.0"}, "repository": {"type": "git", "url": "git+https://forgejo.znetlink.com/znetlink/lantu.git", "directory": "@znetlink/lantu-shared"}, "publishConfig": {"registry": "https://npm.znetlink.com"}}